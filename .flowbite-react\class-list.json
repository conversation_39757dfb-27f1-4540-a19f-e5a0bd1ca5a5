["-4px", "-bottom-1", "-left-1", "-m-1.5", "-mb-px", "-right-1", "-space-x-4", "-top-1", "-translate-x-1/2", "-translate-x-full", "-translate-y-1/2", "-translate-y-full", "-z-10", "2xl", "3xl", "4xl", "5xl", "6xl", "7xl", "absolute", "after:absolute", "after:bg-white", "after:border", "after:border-gray-300", "after:border-transparent", "after:h-4", "after:h-5", "after:h-6", "after:left-0.5", "after:rounded-full", "after:top-0.5", "after:transition-all", "after:translate-x-full", "after:w-4", "after:w-5", "after:w-6", "animate-spin", "appearance-none", "bg-[length:0.55em_0.55em]", "bg-[length:0.75em_0.75em]", "bg-[length:1em_1em]", "bg-[position:right_12px_center]", "bg-arrow-down-icon", "bg-blue-100", "bg-blue-600", "bg-blue-700", "bg-center", "bg-current", "bg-cyan-100", "bg-cyan-50", "bg-cyan-500", "bg-cyan-600", "bg-cyan-700", "bg-dark-50", "bg-dash-icon", "bg-gray-100", "bg-gray-200", "bg-gray-300", "bg-gray-400", "bg-gray-50", "bg-gray-500", "bg-gray-600", "bg-gray-700", "bg-gray-800", "bg-gray-900", "bg-gray-900/50", "bg-green-100", "bg-green-400", "bg-green-50", "bg-green-500", "bg-green-600", "bg-green-700", "bg-indigo-100", "bg-indigo-400", "bg-indigo-600", "bg-indigo-700", "bg-light-50", "bg-lime-100", "bg-lime-400", "bg-lime-600", "bg-lime-700", "bg-no-repeat", "bg-pink-100", "bg-pink-500", "bg-pink-600", "bg-pink-700", "bg-primary-600", "bg-primary-700", "bg-purple-100", "bg-purple-50", "bg-purple-600", "bg-purple-700", "bg-red-100", "bg-red-400", "bg-red-50", "bg-red-600", "bg-red-700", "bg-teal-100", "bg-teal-400", "bg-teal-600", "bg-teal-700", "bg-transparent", "bg-white", "bg-white/30", "bg-white/50", "bg-yellow-100", "bg-yellow-400", "bg-yellow-50", "bg-yellow-600", "block", "border", "border-0", "border-2", "border-b", "border-b-0", "border-b-2", "border-blue-500", "border-blue-700", "border-cyan-500", "border-cyan-700", "border-gray-100", "border-gray-200", "border-gray-300", "border-gray-400", "border-gray-500", "border-gray-600", "border-gray-700", "border-gray-800", "border-green-500", "border-green-700", "border-indigo-500", "border-indigo-700", "border-l-0", "border-lime-500", "border-lime-700", "border-pink-500", "border-pink-700", "border-primary-600", "border-primary-700", "border-purple-500", "border-purple-700", "border-r-0", "border-red-500", "border-red-700", "border-t", "border-t-4", "border-teal-500", "border-teal-700", "border-transparent", "border-white", "border-yellow-400", "border-yellow-500", "bottom-0", "bottom-16", "bottom-5", "bottom-center", "bottom-left", "bottom-right", "center-left", "center-right", "checked:bg-check-icon", "checked:bg-current", "checked:bg-dot-icon", "checked:border-transparent", "container", "cursor-default", "cursor-grab", "cursor-not-allowed", "cursor-pointer", "dark:bg-black", "dark:bg-blue-200", "dark:bg-blue-600", "dark:bg-current", "dark:bg-cyan-100", "dark:bg-cyan-200", "dark:bg-cyan-600", "dark:bg-cyan-900", "dark:bg-dark-900", "dark:bg-gray-200", "dark:bg-gray-300", "dark:bg-gray-400", "dark:bg-gray-500", "dark:bg-gray-600", "dark:bg-gray-700", "dark:bg-gray-800", "dark:bg-gray-800/30", "dark:bg-gray-800/50", "dark:bg-gray-900", "dark:bg-gray-900/80", "dark:bg-green-100", "dark:bg-green-200", "dark:bg-green-500", "dark:bg-green-600", "dark:bg-green-900", "dark:bg-indigo-200", "dark:bg-indigo-500", "dark:bg-indigo-600", "dark:bg-light-900", "dark:bg-lime-200", "dark:bg-lime-600", "dark:bg-pink-200", "dark:bg-pink-600", "dark:bg-primary-600", "dark:bg-purple-200", "dark:bg-purple-500", "dark:bg-purple-600", "dark:bg-purple-900", "dark:bg-red-100", "dark:bg-red-200", "dark:bg-red-500", "dark:bg-red-600", "dark:bg-red-900", "dark:bg-teal-200", "dark:bg-teal-600", "dark:bg-transparent", "dark:bg-yellow-100", "dark:bg-yellow-200", "dark:bg-yellow-600", "dark:bg-yellow-900", "dark:border-blue-500", "dark:border-cyan-400", "dark:border-cyan-500", "dark:border-gray-600", "dark:border-gray-700", "dark:border-gray-800", "dark:border-green-400", "dark:border-green-600", "dark:border-indigo-600", "dark:border-lime-600", "dark:border-none", "dark:border-pink-600", "dark:border-primary-500", "dark:border-primary-600", "dark:border-purple-600", "dark:border-red-400", "dark:border-red-600", "dark:border-teal-600", "dark:border-transparent", "dark:border-yellow-300", "dark:border-yellow-400", "dark:checked:bg-current", "dark:checked:border-transparent", "dark:divide-gray-700", "dark:file:bg-gray-600", "dark:fill-gray-300", "dark:focus:bg-gray-600", "dark:focus:border-cyan-500", "dark:focus:border-green-500", "dark:focus:border-primary-500", "dark:focus:border-red-500", "dark:focus:border-yellow-500", "dark:focus:ring-blue-700", "dark:focus:ring-blue-800", "dark:focus:ring-cyan-500", "dark:focus:ring-cyan-600", "dark:focus:ring-cyan-800", "dark:focus:ring-gray-600", "dark:focus:ring-gray-700", "dark:focus:ring-gray-800", "dark:focus:ring-gray-900", "dark:focus:ring-green-500", "dark:focus:ring-green-600", "dark:focus:ring-green-800", "dark:focus:ring-indigo-700", "dark:focus:ring-indigo-800", "dark:focus:ring-lime-700", "dark:focus:ring-lime-800", "dark:focus:ring-pink-600", "dark:focus:ring-pink-800", "dark:focus:ring-primary-500", "dark:focus:ring-primary-600", "dark:focus:ring-primary-800", "dark:focus:ring-purple-600", "dark:focus:ring-purple-800", "dark:focus:ring-red-500", "dark:focus:ring-red-600", "dark:focus:ring-red-800", "dark:focus:ring-red-900", "dark:focus:ring-teal-600", "dark:focus:ring-teal-800", "dark:focus:ring-yellow-400", "dark:focus:ring-yellow-500", "dark:focus:ring-yellow-900", "dark:focus:text-white", "dark:group-focus:ring-blue-800", "dark:group-focus:ring-cyan-800", "dark:group-focus:ring-gray-800", "dark:group-focus:ring-gray-800/70", "dark:group-focus:ring-green-800", "dark:group-focus:ring-indigo-800", "dark:group-focus:ring-lime-800", "dark:group-focus:ring-pink-800", "dark:group-focus:ring-primary-800", "dark:group-focus:ring-purple-800", "dark:group-focus:ring-red-800", "dark:group-focus:ring-teal-800", "dark:group-focus:ring-yellow-800", "dark:group-hover:bg-gray-800/60", "dark:group-hover:text-white", "dark:hover:bg-blue-300", "dark:hover:bg-blue-700", "dark:hover:bg-cyan-300", "dark:hover:bg-cyan-700", "dark:hover:bg-gray-300", "dark:hover:bg-gray-500", "dark:hover:bg-gray-600", "dark:hover:bg-gray-700", "dark:hover:bg-gray-800", "dark:hover:bg-green-300", "dark:hover:bg-green-700", "dark:hover:bg-indigo-300", "dark:hover:bg-indigo-700", "dark:hover:bg-lime-300", "dark:hover:bg-lime-700", "dark:hover:bg-pink-300", "dark:hover:bg-pink-700", "dark:hover:bg-primary-700", "dark:hover:bg-purple-300", "dark:hover:bg-purple-700", "dark:hover:bg-red-300", "dark:hover:bg-red-700", "dark:hover:bg-teal-300", "dark:hover:bg-teal-700", "dark:hover:bg-yellow-300", "dark:hover:bg-yellow-400", "dark:hover:border-blue-700", "dark:hover:border-cyan-700", "dark:hover:border-gray-600", "dark:hover:border-gray-700", "dark:hover:border-green-700", "dark:hover:border-indigo-700", "dark:hover:border-lime-700", "dark:hover:border-pink-700", "dark:hover:border-primary-700", "dark:hover:border-purple-700", "dark:hover:border-red-700", "dark:hover:border-teal-700", "dark:hover:border-yellow-400", "dark:hover:file:bg-gray-500", "dark:hover:text-gray-300", "dark:hover:text-white", "dark:mix-blend-color", "dark:placeholder-gray-400", "dark:ring-cyan-800", "dark:ring-gray-400", "dark:ring-gray-500", "dark:ring-gray-800", "dark:ring-green-500", "dark:ring-offset-blue-700", "dark:ring-offset-cyan-600", "dark:ring-offset-gray-800", "dark:ring-offset-gray-900", "dark:ring-offset-green-600", "dark:ring-offset-green-800", "dark:ring-offset-indigo-700", "dark:ring-offset-lime-700", "dark:ring-offset-pink-600", "dark:ring-offset-purple-600", "dark:ring-offset-red-600", "dark:ring-offset-red-900", "dark:ring-offset-teal-600", "dark:ring-offset-yellow-400", "dark:ring-pink-500", "dark:ring-purple-600", "dark:ring-red-700", "dark:ring-yellow-500", "dark:shadow-sm-light", "dark:text-blue-500", "dark:text-blue-600", "dark:text-blue-800", "dark:text-blue-900", "dark:text-cyan-500", "dark:text-cyan-600", "dark:text-cyan-800", "dark:text-cyan-900", "dark:text-gray-100", "dark:text-gray-200", "dark:text-gray-300", "dark:text-gray-400", "dark:text-gray-600", "dark:text-gray-800", "dark:text-gray-900", "dark:text-green-500", "dark:text-green-600", "dark:text-green-800", "dark:text-green-900", "dark:text-indigo-400", "dark:text-indigo-600", "dark:text-indigo-800", "dark:text-indigo-900", "dark:text-lime-500", "dark:text-lime-600", "dark:text-lime-800", "dark:text-lime-900", "dark:text-pink-500", "dark:text-pink-600", "dark:text-pink-800", "dark:text-pink-900", "dark:text-primary-100", "dark:text-primary-500", "dark:text-purple-400", "dark:text-purple-600", "dark:text-purple-800", "dark:text-purple-900", "dark:text-red-500", "dark:text-red-600", "dark:text-red-800", "dark:text-red-900", "dark:text-teal-400", "dark:text-teal-600", "dark:text-teal-800", "dark:text-teal-900", "dark:text-white", "dark:text-yellow-300", "dark:text-yellow-600", "dark:text-yellow-800", "dark:text-yellow-900", "delay-0", "disabled:cursor-not-allowed", "disabled:dark:text-gray-500", "disabled:opacity-50", "disabled:text-gray-400", "divide-gray-100", "divide-gray-200", "divide-x", "divide-y", "drop-shadow-md", "duration-75", "ease-in-out", "end-2.5", "even:bg-gray-50", "even:dark:bg-gray-700", "file:-ms-4", "file:bg-gray-800", "file:border-none", "file:cursor-pointer", "file:font-medium", "file:leading-[inherit]", "file:me-4", "file:pe-4", "file:ps-8", "file:py-2.5", "file:text-sm", "file:text-white", "fill-cyan-600", "fill-gray-600", "fill-green-500", "fill-pink-600", "fill-primary-600", "fill-purple-600", "fill-red-600", "fill-yellow-400", "first:border-l", "first:border-t-0", "first:ml-0", "first:mt-0", "first:pt-0", "first:rounded-s-lg", "first:rounded-t-lg", "fixed", "flex", "flex-1", "flex-col", "flex-wrap", "focus:bg-gray-100", "focus:border-cyan-500", "focus:border-green-500", "focus:border-primary-500", "focus:border-red-500", "focus:border-yellow-500", "focus:outline-none", "focus:ring-1", "focus:ring-2", "focus:ring-4", "focus:ring-blue-300", "focus:ring-blue-400", "focus:ring-blue-600", "focus:ring-cyan-300", "focus:ring-cyan-400", "focus:ring-cyan-500", "focus:ring-cyan-600", "focus:ring-cyan-800", "focus:ring-gray-100", "focus:ring-gray-200", "focus:ring-gray-300", "focus:ring-gray-400", "focus:ring-gray-800", "focus:ring-gray-900", "focus:ring-green-300", "focus:ring-green-400", "focus:ring-green-500", "focus:ring-green-600", "focus:ring-green-800", "focus:ring-indigo-300", "focus:ring-indigo-400", "focus:ring-indigo-700", "focus:ring-lime-300", "focus:ring-lime-400", "focus:ring-lime-700", "focus:ring-offset-2", "focus:ring-pink-300", "focus:ring-pink-400", "focus:ring-pink-600", "focus:ring-primary-300", "focus:ring-primary-500", "focus:ring-primary-600", "focus:ring-purple-300", "focus:ring-purple-400", "focus:ring-purple-600", "focus:ring-red-300", "focus:ring-red-400", "focus:ring-red-500", "focus:ring-red-600", "focus:ring-red-900", "focus:ring-teal-300", "focus:ring-teal-400", "focus:ring-teal-600", "focus:ring-yellow-300", "focus:ring-yellow-400", "focus:ring-yellow-500", "font-bold", "font-medium", "font-normal", "font-semibold", "gap-1", "gap-2", "gap-4", "grid", "grid-cols-4", "grid-cols-7", "grid-flow-col", "group", "group-first/body:group-first/row:first:rounded-tl-lg", "group-first/body:group-first/row:last:rounded-tr-lg", "group-first/head:first:rounded-tl-lg", "group-first/head:last:rounded-tr-lg", "group-focus:outline-none", "group-focus:ring-4", "group-focus:ring-blue-300", "group-focus:ring-cyan-300", "group-focus:ring-gray-300", "group-focus:ring-green-300", "group-focus:ring-indigo-300", "group-focus:ring-lime-300", "group-focus:ring-pink-300", "group-focus:ring-primary-300", "group-focus:ring-purple-300", "group-focus:ring-red-300", "group-focus:ring-teal-300", "group-focus:ring-white", "group-focus:ring-yellow-300", "group-hover:bg-white/50", "group-hover:text-gray-900", "group-last/body:group-last/row:first:rounded-bl-lg", "group-last/body:group-last/row:last:rounded-br-lg", "group/body", "group/head", "group/row", "h-1", "h-1.5", "h-10", "h-12", "h-2", "h-2.5", "h-20", "h-3", "h-3.5", "h-36", "h-4", "h-5", "h-6", "h-7", "h-8", "h-9", "h-96", "h-[52px]", "h-auto", "h-fit", "h-full", "h-px", "h-screen", "hidden", "hover:bg-blue-200", "hover:bg-blue-800", "hover:bg-cyan-200", "hover:bg-cyan-800", "hover:bg-gray-100", "hover:bg-gray-200", "hover:bg-gray-300", "hover:bg-gray-50", "hover:bg-gray-500", "hover:bg-gray-600", "hover:bg-gray-800", "hover:bg-gray-900", "hover:bg-green-200", "hover:bg-green-800", "hover:bg-indigo-200", "hover:bg-indigo-800", "hover:bg-lime-200", "hover:bg-lime-800", "hover:bg-pink-200", "hover:bg-pink-800", "hover:bg-primary-600", "hover:bg-primary-800", "hover:bg-purple-200", "hover:bg-purple-800", "hover:bg-red-200", "hover:bg-red-800", "hover:bg-teal-200", "hover:bg-teal-800", "hover:bg-white", "hover:bg-yellow-200", "hover:bg-yellow-500", "hover:border-blue-800", "hover:border-cyan-800", "hover:border-gray-300", "hover:border-gray-800", "hover:border-gray-900", "hover:border-green-800", "hover:border-indigo-800", "hover:border-lime-800", "hover:border-pink-800", "hover:border-primary-800", "hover:border-purple-800", "hover:border-red-800", "hover:border-teal-800", "hover:border-yellow-500", "hover:cursor-not-allowed", "hover:file:bg-gray-700", "hover:text-gray-600", "hover:text-gray-700", "hover:text-gray-900", "hover:text-primary-700", "hover:text-white", "inline", "inline-block", "inline-flex", "inset-0", "inset-x-0", "inset-y-0", "invisible", "items-center", "items-end", "items-start", "justify-between", "justify-center", "justify-end", "justify-start", "last:rounded-b-lg", "last:rounded-e-lg", "leading-6", "leading-9", "leading-none", "left-0", "left-1/2", "list-decimal", "list-disc", "list-inside", "list-none", "max-h-[90dvh]", "max-w-2xl", "max-w-3xl", "max-w-4xl", "max-w-5xl", "max-w-6xl", "max-w-7xl", "max-w-[100vw]", "max-w-lg", "max-w-md", "max-w-sm", "max-w-xl", "mb-1", "mb-2", "mb-4", "mb-5", "md:bg-transparent", "md:block", "md:border-0", "md:dark:hover:bg-transparent", "md:dark:hover:text-white", "md:flex-row", "md:font-medium", "md:h-auto", "md:h-full", "md:hidden", "md:hover:bg-transparent", "md:hover:text-primary-700", "md:inset-0", "md:max-w-xl", "md:mt-0", "md:my-10", "md:my-12", "md:p-0", "md:rounded-l-lg", "md:rounded-none", "md:space-x-8", "md:text-primary-700", "md:text-sm", "md:w-48", "md:w-auto", "me-2", "me-2.5", "min-w-11", "min-w-9", "min-w-[52px]", "mix-blend-lighten", "ml-0", "ml-2", "ml-3", "ml-auto", "mr-2", "mr-3", "ms-3", "mt-0.5", "mt-2", "mt-4", "mt-6", "mx-auto", "my-1", "my-4", "my-8", "object-cover", "odd:bg-white", "odd:dark:bg-gray-800", "opacity-0", "opacity-50", "outline-none", "overflow-auto", "overflow-hidden", "overflow-x-hidden", "overflow-x-scroll", "overflow-y-auto", "overflow-y-hidden", "p-1", "p-1.5", "p-2", "p-2.5", "p-4", "p-5", "p-6", "pl-10", "pl-2.5", "pl-3", "pl-8", "placeholder-cyan-700", "placeholder-gray-500", "placeholder-green-700", "placeholder-red-700", "placeholder-yellow-700", "pointer-events-none", "pr-10", "pr-3", "pr-4", "ps-5", "pt-0", "pt-2", "pt-4", "px-2", "px-3", "px-4", "px-5", "px-6", "py-0.5", "py-1", "py-2", "py-2.5", "py-3", "py-4", "relative", "right-0", "ring-2", "ring-cyan-400", "ring-gray-300", "ring-gray-500", "ring-gray-800", "ring-green-500", "ring-pink-500", "ring-purple-500", "ring-red-500", "ring-yellow-300", "rotate-180", "rotate-45", "rounded", "rounded-[7px]", "rounded-b", "rounded-full", "rounded-l-md", "rounded-lg", "rounded-md", "rounded-none", "rounded-r-lg", "rounded-t", "rounded-t-lg", "rtl:after:-translate-x-full", "rtl:after:right-0.5", "scroll-smooth", "self-center", "shadow", "shadow-lg", "shadow-md", "shadow-sm", "shrink-0", "sm:h-10", "sm:h-6", "sm:h-7", "sm:px-4", "sm:text-base", "sm:text-xs", "sm:w-10", "sm:w-6", "snap-center", "snap-mandatory", "snap-x", "space-x-2", "space-x-3", "space-x-4", "space-y-0", "space-y-1", "space-y-2", "sr-only", "text-base", "text-blue-500", "text-blue-700", "text-blue-800", "text-center", "text-cyan-500", "text-cyan-600", "text-cyan-700", "text-cyan-800", "text-cyan-900", "text-gray-100", "text-gray-200", "text-gray-400", "text-gray-500", "text-gray-600", "text-gray-700", "text-gray-800", "text-gray-900", "text-green-500", "text-green-600", "text-green-700", "text-green-800", "text-green-900", "text-indigo-500", "text-indigo-700", "text-indigo-800", "text-left", "text-lg", "text-lime-500", "text-lime-700", "text-lime-800", "text-pink-500", "text-pink-600", "text-pink-700", "text-pink-800", "text-primary-300", "text-primary-600", "text-primary-700", "text-purple-500", "text-purple-600", "text-purple-700", "text-purple-800", "text-red-500", "text-red-600", "text-red-700", "text-red-800", "text-red-900", "text-sm", "text-start", "text-teal-500", "text-teal-600", "text-teal-700", "text-teal-800", "text-white", "text-xl", "text-xs", "text-yellow-400", "text-yellow-500", "text-yellow-700", "text-yellow-800", "text-yellow-900", "top-0", "top-1/2", "top-10", "top-2.5", "top-center", "top-left", "top-right", "transform", "transform-none", "transition", "transition-opacity", "transition-transform", "translate-x-full", "translate-y-full", "uppercase", "w-10", "w-11", "w-16", "w-2", "w-20", "w-3", "w-3.5", "w-36", "w-4", "w-48", "w-5", "w-6", "w-64", "w-8", "w-80", "w-9", "w-[52px]", "w-auto", "w-fit", "w-full", "w-max", "whitespace-nowrap", "z-0", "z-10", "z-20", "z-30", "z-40", "z-50", "z-auto"]