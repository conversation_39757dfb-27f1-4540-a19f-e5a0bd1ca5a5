import { Suspense, memo, useState, useEffect } from 'react';

/**
 * Optimized component loader with intelligent loading states
 */

// Loading skeleton components
// Provides pre-designed loading placeholders to improve perceived performance.
export const LoadingSkeleton = memo(({ type = 'default', className = '' }) => {
    const skeletons = {
        default: (
            <div className={`animate-pulse ${className}`}>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
        ),
        button: (
            <div className={`animate-pulse ${className}`}>
                <div className="h-10 bg-gray-200 rounded w-24"></div>
            </div>
        ),
        card: (
            <div className={`animate-pulse ${className}`}>
                <div className="border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-20 bg-gray-200 rounded"></div>
                </div>
            </div>
        ),
        table: (
            <div className={`animate-pulse ${className}`}>
                <div className="space-y-2">
                    <div className="h-8 bg-gray-200 rounded"></div>
                    <div className="h-6 bg-gray-100 rounded"></div>
                    <div className="h-6 bg-gray-100 rounded"></div>
                    <div className="h-6 bg-gray-100 rounded"></div>
                </div>
            </div>
        ),
        sidebar: (
            <div className={`animate-pulse ${className}`}>
                <div className="w-16 bg-gray-200 h-full rounded"></div>
            </div>
        ),
        navbar: (
            <div className={`animate-pulse ${className}`}>
                <div className="h-14 bg-gray-200 rounded"></div>
            </div>
        ),
        chart: (
            <div className={`animate-pulse ${className}`}>
                <div className="h-64 bg-gray-200 rounded flex items-center justify-center">
                    <div className="text-gray-400">Loading chart...</div>
                </div>
            </div>
        ),
    };

    return skeletons[type] || skeletons.default;
});

// Progressive loading component
// Adds intelligent delays before showing fallbacks to prevent UI flickering and handles loading timeouts.
export const ProgressiveLoader = memo(({
    children,
    fallback,
    delay = 200,
    timeout = 10000,
    onTimeout,
    className = ''
}) => {
    const [showFallback, setShowFallback] = useState(false);
    const [timedOut, setTimedOut] = useState(false);

    useEffect(() => {
        const delayTimer = setTimeout(() => setShowFallback(true), delay);
        const timeoutTimer = setTimeout(() => {
            setTimedOut(true);
            onTimeout?.();
        }, timeout);

        return () => {
            clearTimeout(delayTimer);
            clearTimeout(timeoutTimer);
        };
    }, [delay, timeout, onTimeout]);

    if (timedOut) {
        return (
            <div className={`text-center p-4 ${className}`}>
                <div className="text-red-500">Loading timeout. Please refresh the page.</div>
            </div>
        );
    }

    return (
        <Suspense fallback={showFallback ? fallback : null}>
            {children}
        </Suspense>
    );
});

// Smart component loader with caching
// The main component for dynamic imports with intelligent caching, error handling, and customizable loading states.
const componentCache = new Map();

export const SmartLoader = memo(({
    importFunction,
    componentName,
    fallbackType = 'default',
    className = '',
    cacheKey,
    ...props
}) => {
    const [Component, setComponent] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const loadComponent = async () => {
            try {
                const key = cacheKey || componentName;

                // Check cache first
                if (componentCache.has(key)) {
                    setComponent(componentCache.get(key));
                    setLoading(false);
                    return;
                }

                const module = await importFunction();
                const LoadedComponent = module.default || module;

                // Cache the component
                componentCache.set(key, LoadedComponent);
                setComponent(() => LoadedComponent);
                setLoading(false);
            } catch (err) {
                console.error(`Failed to load component ${componentName}:`, err);
                setError(err);
                setLoading(false);
            }
        };

        loadComponent();
    }, [importFunction, componentName, cacheKey]);

    if (error) {
        return (
            <div className={`text-center p-4 text-red-500 ${className}`}>
                Failed to load {componentName}
            </div>
        );
    }

    if (loading || !Component) {
        return <LoadingSkeleton type={fallbackType} className={className} />;
    }

    return <Component {...props} />;
});

// Intersection Observer based lazy loader
// Loads components only when they become visible in the viewport using Intersection Observer API.
export const LazyLoader = memo(({
    children,
    fallback,
    rootMargin = '50px',
    threshold = 0.1,
    className = ''
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [ref, setRef] = useState(null);

    useEffect(() => {
        if (!ref) return;

        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { rootMargin, threshold }
        );

        observer.observe(ref);

        return () => observer.disconnect();
    }, [ref, rootMargin, threshold]);

    return (
        <div ref={setRef} className={className}>
            {isVisible ? children : fallback}
        </div>
    );
});

// Preloader for critical components
// Manually preload a single component into cache.
export const preloadComponent = (importFunction, cacheKey) => {
    if (!componentCache.has(cacheKey)) {
        importFunction()
            .then(module => {
                const Component = module.default || module;
                componentCache.set(cacheKey, Component);
            })
            .catch(error => {
                console.warn(`Failed to preload component ${cacheKey}:`, error);
            });
    }
};

// Batch preloader
// Preload multiple components in batch.
export const preloadComponents = (components) => {
    components.forEach(({ importFunction, cacheKey }) => {
        preloadComponent(importFunction, cacheKey);
    });
};

// Default export for main loader
export default SmartLoader;
