import defaultTheme from "tailwindcss/defaultTheme";
import forms from "@tailwindcss/forms";

/** @type {import('tailwindcss').Config} */
const flowbite = require("flowbite-react/tailwind");

export default {
    content: ["./resources/**/*.{blade.php,php,jsx,js}", flowbite.content()],

    theme: {
        extend: {
            fontFamily: {
                sans: ["Figtree", ...defaultTheme.fontFamily.sans],
            },
            width: {
                // '96': '32rem',
            },
            gridTemplateColumns: {
                // 24 column grid
                16: "repeat(16, minmax(0, 1fr))",
            },
            height: {
                '10vh': '10vh',
                '20vh': '20vh',
                '30vh': '30vh',
                '40vh': '40vh',
                '50vh': '50vh',
                '60vh': '60vh',
                '70vh': '70vh',
                '80vh': '80vh',
                '90vh': '90vh',
                // Add more as needed
            },

            // colors: {
            //     red: {
            //         950: '#800000',
            //         900: '#A52A2A',
            //         800: '#DC143C',
            //         700: '#FF0000',
            //         600: '#FF6347',
            //         500: '#FF8C69',
            //         400: '#FA8072',
            //     },
            //     orange: {
            //         950: '#8B4513',
            //         900: '#D2691E',
            //         800: '#FF4500',
            //         700: '#FF8C00',
            //         600: '#FFA500',
            //         500: '#FFD700',
            //         400: '#FFE4B5',
            //     },
            //     amber: {
            //         950: '#B8860B',
            //         900: '#DAA520',
            //         800: '#FF8C00',
            //         700: '#FFB84D',
            //         600: '#FFD700',
            //         500: '#FFFACD',
            //         400: '#FAF0E6',
            //     },
            //     yellow: {
            //         950: '#808000',
            //         900: '#BDB76B',
            //         800: '#FFFF00',
            //         700: '#FFF8DC',
            //         600: '#FFFFE0',
            //         500: '#FFFACD',
            //         400: '#FFFFF0',
            //     },
            //     lime: {
            //         950: '#556B2F',
            //         900: '#9ACD32',
            //         800: '#00FF00',
            //         700: '#7CFC00',
            //         600: '#ADFF2F',
            //         500: '#F0FFF0',
            //         400: '#E0EEE0',
            //     },
            //     green: {
            //         950: '#006400',
            //         900: '#228B22',
            //         800: '#008000',
            //         700: '#00FF00',
            //         600: '#3CB371',
            //         500: '#90EE90',
            //         400: '#F5FFFA',
            //     },
            //     emerald: {
            //         950: '#2F4F4F',
            //         900: '#008080',
            //         800: '#008B8B',
            //         700: '#00CED1',
            //         600: '#20B2AA',
            //         500: '#7FFFD4',
            //         400: '#AFEEEE',
            //     },
            //     teal: {
            //         950: '#008080',
            //         900: '#008B8B',
            //         800: '#00CED1',
            //         700: '#20B2AA',
            //         600: '#48D1CC',
            //         500: '#ADD8E6',
            //         400: '#B0E0E6',
            //     },
            //     cyan: {
            //         950: '#00FFFF',
            //         900: '#00FFFF',
            //         800: '#00FFFF',
            //         700: '#00FFFF',
            //         600: '#00FFFF',
            //         500: '#ADD8E6',
            //         400: '#B0E0E6',
            //     },
            //     sky: {
            //         950: '#87CEEB',
            //         900: '#87CEEB',
            //         800: '#87CEEB',
            //         700: '#87CEEB',
            //         600: '#87CEEB',
            //         500: '#ADD8E6',
            //         400: '#B0E0E6',
            //     },
            // },
        },
    },

    plugins: [
        forms,
        flowbite.plugin(),
    ],
};
